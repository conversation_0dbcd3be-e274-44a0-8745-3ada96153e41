package dev.pigmomo.yhkit2025

import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.lifecycle.ViewModelProvider
import dev.pigmomo.yhkit2025.data.database.AppDatabase
import dev.pigmomo.yhkit2025.data.repository.TokenRepositoryImpl
import dev.pigmomo.yhkit2025.service.BackgroundService
import dev.pigmomo.yhkit2025.ui.screens.MainScreen
import dev.pigmomo.yhkit2025.ui.theme.Yhkit2025Theme
import dev.pigmomo.yhkit2025.utils.PermissionUtils
import dev.pigmomo.yhkit2025.viewmodel.MainViewModel
import android.Manifest
import android.content.pm.PackageManager
import android.os.Build
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import android.widget.Toast

class MainActivity : ComponentActivity() {
    
    private lateinit var viewModel: MainViewModel
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 请求通知权限（Android 13及以上需要）
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            requestNotificationPermission()
        }

        // 请求存储权限（如果需要）
        if (!PermissionUtils.hasStoragePermission(this)) {
            PermissionUtils.requestStoragePermission(this)
        }
        
        // 初始化数据库、仓库和ViewModel
        val database = AppDatabase.getDatabase(applicationContext)
        val configTokenDao = database.configTokenDao()
        
        val tokenRepository = TokenRepositoryImpl(configTokenDao = configTokenDao)
        
        viewModel = ViewModelProvider(
            this,
            MainViewModel.Factory(tokenRepository, application)
        )[MainViewModel::class.java]    
        
        // 启动后台服务
        BackgroundService.startService(this)
        
        // 设置MainViewModel启动OrderActivity的方法
        viewModel.startOrderActivityCallback = { startOrderActivity() }
        
        // 设置MainViewModel启动LogViewActivity的方法
        viewModel.startLogViewActivityCallback = { startLogViewActivity() }
        
        // 设置MainViewModel启动LoginActivity的方法
        viewModel.startLoginActivityCallback = { startLoginActivity() }
        
        setContent {
            Yhkit2025Theme(dynamicColor = false) {
                MainScreen(viewModel)
            }
        }
    }
    
    // 启动订单管理Activity
    private fun startOrderActivity() {
        val intent = Intent(this, OrderActivity::class.java)
        startActivity(intent)
    }
    
    // 启动日志查看Activity
    private fun startLogViewActivity() {
        val intent = Intent(this, LogViewActivity::class.java)
        startActivity(intent)
    }
    
    // 启动登录账号管理Activity
    private fun startLoginActivity() {
        val intent = Intent(this, LoginActivity::class.java)
        startActivity(intent)
    }

    // 请求通知权限
    private fun requestNotificationPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (ContextCompat.checkSelfPermission(
                    this,
                    Manifest.permission.POST_NOTIFICATIONS
                ) != PackageManager.PERMISSION_GRANTED
            ) {
                ActivityCompat.requestPermissions(
                    this,
                    arrayOf(Manifest.permission.POST_NOTIFICATIONS),
                    100
                )
            }
        }
    }

    // 处理权限请求结果
    @Deprecated("Deprecated in Java")
    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        when (requestCode) {
            100 -> {
                // 通知权限
                if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    // 权限已授予，可以显示通知
                    Toast.makeText(this, "通知权限授予成功", Toast.LENGTH_SHORT).show()
                    // 重启前台服务以确保通知显示
                    BackgroundService.stopService(this)
                    BackgroundService.startService(this)
                } else {
                    // 权限被拒绝
                    Toast.makeText(this, "通知权限被拒绝，部分功能可能无法正常工作", Toast.LENGTH_LONG).show()
                }
            }
            PermissionUtils.REQUEST_CODE_STORAGE_PERMISSION -> {
                // 存储权限
                if (PermissionUtils.onRequestPermissionsResult(requestCode, permissions, grantResults)) {
                    Toast.makeText(this, "存储权限授予成功，现在可以导出文件到下载目录", Toast.LENGTH_SHORT).show()
                } else {
                    Toast.makeText(this, "存储权限被拒绝，文件将保存到应用私有目录", Toast.LENGTH_LONG).show()
                }
            }
        }
    }
}


