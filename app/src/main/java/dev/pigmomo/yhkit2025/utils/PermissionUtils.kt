package dev.pigmomo.yhkit2025.utils

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.pm.PackageManager
import android.os.Build
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat

/**
 * 权限管理工具类
 * 用于处理运行时权限请求和检查
 */
object PermissionUtils {
    
    // 权限请求码
    const val REQUEST_CODE_STORAGE_PERMISSION = 200
    
    /**
     * 检查是否有存储权限
     * @param context 上下文
     * @return 是否有存储权限
     */
    fun hasStoragePermission(context: Context): Bo<PERSON>an {
        return when {
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU -> {
                // Android 13+ 不需要存储权限来写入公共Downloads目录
                true
            }
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q -> {
                // Android 10-12 使用分区存储，不需要WRITE_EXTERNAL_STORAGE来写入Downloads
                true
            }
            else -> {
                // Android 9及以下需要WRITE_EXTERNAL_STORAGE权限
                ContextCompat.checkSelfPermission(
                    context,
                    Manifest.permission.WRITE_EXTERNAL_STORAGE
                ) == PackageManager.PERMISSION_GRANTED
            }
        }
    }
    
    /**
     * 请求存储权限
     * @param activity Activity实例
     */
    fun requestStoragePermission(activity: Activity) {
        when {
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU -> {
                // Android 13+ 不需要请求存储权限
                return
            }
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q -> {
                // Android 10-12 不需要请求存储权限
                return
            }
            else -> {
                // Android 9及以下请求WRITE_EXTERNAL_STORAGE权限
                ActivityCompat.requestPermissions(
                    activity,
                    arrayOf(Manifest.permission.WRITE_EXTERNAL_STORAGE),
                    REQUEST_CODE_STORAGE_PERMISSION
                )
            }
        }
    }
    
    /**
     * 检查权限请求结果
     * @param requestCode 请求码
     * @param permissions 权限数组
     * @param grantResults 授权结果数组
     * @return 是否授权成功
     */
    fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ): Boolean {
        if (requestCode == REQUEST_CODE_STORAGE_PERMISSION) {
            return grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED
        }
        return false
    }
    
    /**
     * 检查是否应该显示权限说明
     * @param activity Activity实例
     * @return 是否应该显示权限说明
     */
    fun shouldShowStoragePermissionRationale(activity: Activity): Boolean {
        return when {
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q -> {
                // Android 10+ 不需要显示权限说明
                false
            }
            else -> {
                ActivityCompat.shouldShowRequestPermissionRationale(
                    activity,
                    Manifest.permission.WRITE_EXTERNAL_STORAGE
                )
            }
        }
    }
}
